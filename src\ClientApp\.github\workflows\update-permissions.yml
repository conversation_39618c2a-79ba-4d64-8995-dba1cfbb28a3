name: 🔄 Atualizar Mapeamento de Permissões

on:
  # Execução manual
  workflow_dispatch:
    inputs:
      changes_description:
        description: '<PERSON>cre<PERSON> as alterações realizadas nos arquivos de Funcionalidades'
        required: true
        type: string
        default: 'Atualizações nas ações e permissões'
      
      update_excel:
        description: 'Atualizar planilha Excel'
        required: false
        type: boolean
        default: true
      
      update_html:
        description: 'Atualizar página HTML'
        required: false
        type: boolean
        default: true
      
      update_csv:
        description: 'Atualizar arquivo CSV'
        required: false
        type: boolean
        default: true

  # Execução automática quando arquivos da pasta Funcionalidades são modificados
  push:
    paths:
      - 'src/constants/Funcionalidades/**/*.ts'
      - 'src/constants/Enum/enumTipoUsuario.ts'
    branches:
      - main
      - develop

  # Execução automática em Pull Requests
  pull_request:
    paths:
      - 'src/constants/Funcionalidades/**/*.ts'
      - 'src/constants/Enum/enumTipoUsuario.ts'

jobs:
  update-permissions:
    name: 📊 Atualizar Planilhas e HTML
    runs-on: ubuntu-latest
    
    defaults:
      run:
        working-directory: ./src/ClientApp
    
    steps:
      - name: 📥 Checkout do código
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📋 Detectar alterações
        id: changes
        run: |
          echo "Detectando alterações nos arquivos de Funcionalidades..."
          
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "changes=${{ github.event.inputs.changes_description }}" >> $GITHUB_OUTPUT
          else
            # Detectar arquivos modificados automaticamente
            CHANGED_FILES=$(git diff --name-only HEAD~1 HEAD | grep -E "(Funcionalidades|enumTipoUsuario)" || echo "")
            if [ -n "$CHANGED_FILES" ]; then
              echo "changes=Arquivos modificados: $CHANGED_FILES" >> $GITHUB_OUTPUT
            else
              echo "changes=Atualizações automáticas detectadas" >> $GITHUB_OUTPUT
            fi
          fi

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: ./src/ClientApp/package-lock.json

      - name: 🐍 Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
          cache: 'pip'

      - name: 📦 Instalar dependências Node.js
        run: |
          if [ -f package.json ]; then
            npm ci
          else
            npm install xlsx
          fi

      - name: 📦 Instalar dependências Python
        run: |
          python -m pip install --upgrade pip
          pip install pandas openpyxl

      - name: 🔍 Analisar arquivos de Funcionalidades
        id: analyze
        run: |
          echo "📊 Analisando arquivos da pasta Funcionalidades..."
          
          # Contar arquivos
          TOTAL_FILES=$(find src/constants/Funcionalidades -name "*.ts" | wc -l)
          echo "total_files=$TOTAL_FILES" >> $GITHUB_OUTPUT
          
          # Listar arquivos
          echo "📁 Arquivos encontrados:"
          find src/constants/Funcionalidades -name "*.ts" -exec basename {} \;
          
          echo "analysis_complete=true" >> $GITHUB_OUTPUT

      - name: 📊 Gerar planilha Excel (Node.js)
        if: ${{ github.event.inputs.update_excel != 'false' }}
        run: |
          echo "🔄 Gerando planilha Excel com Node.js..."
          node gerar_excel.cjs
          
          if [ -f "Mapeamento_Acoes_Funcionalidades_com_Constantes.xlsx" ]; then
            echo "✅ Planilha Excel gerada com sucesso"
            ls -la *.xlsx
          else
            echo "❌ Erro ao gerar planilha Excel"
            exit 1
          fi

      - name: 📊 Gerar planilha Excel (Python)
        if: ${{ github.event.inputs.update_excel != 'false' }}
        run: |
          echo "🔄 Validando com script Python..."
          python gerar_planilha_acoes.py
          echo "✅ Validação Python concluída"

      - name: 📄 Gerar arquivo CSV
        if: ${{ github.event.inputs.update_csv != 'false' }}
        run: |
          echo "🔄 Gerando arquivo CSV..."
          node gerar_csv_com_constantes.cjs
          
          if [ -f "Mapeamento_Acoes_Funcionalidades_com_Constantes.csv" ]; then
            echo "✅ Arquivo CSV gerado com sucesso"
            wc -l *.csv
          else
            echo "❌ Erro ao gerar arquivo CSV"
            exit 1
          fi

      - name: 🌐 Validar página HTML
        if: ${{ github.event.inputs.update_html != 'false' }}
        run: |
          echo "🔄 Validando página HTML..."
          
          if [ -f "Mapeamento_Acoes_Funcionalidades_com_Constantes.html" ]; then
            echo "✅ Página HTML encontrada"
            # Verificar se contém dados
            if grep -q "acoesData" Mapeamento_Acoes_Funcionalidades_com_Constantes.html; then
              echo "✅ Página HTML contém dados das ações"
            else
              echo "⚠️ Página HTML pode estar desatualizada"
            fi
          else
            echo "❌ Página HTML não encontrada"
          fi

      - name: 📈 Gerar estatísticas
        id: stats
        run: |
          echo "📊 Gerando estatísticas..."
          
          # Contar ações no CSV
          if [ -f "Mapeamento_Acoes_Funcionalidades_com_Constantes.csv" ]; then
            TOTAL_ACTIONS=$(tail -n +2 Mapeamento_Acoes_Funcionalidades_com_Constantes.csv | wc -l)
            echo "total_actions=$TOTAL_ACTIONS" >> $GITHUB_OUTPUT
            echo "📊 Total de ações mapeadas: $TOTAL_ACTIONS"
          fi
          
          # Verificar arquivos gerados
          echo "📁 Arquivos gerados:"
          ls -la *.xlsx *.csv *.html 2>/dev/null || echo "Alguns arquivos podem não ter sido gerados"

      - name: 💾 Commit e Push (se executado automaticamente)
        if: ${{ github.event_name != 'workflow_dispatch' && github.event_name != 'pull_request' }}
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          
          # Adicionar arquivos gerados
          git add *.xlsx *.csv *.html 2>/dev/null || true
          
          # Verificar se há mudanças
          if git diff --staged --quiet; then
            echo "📝 Nenhuma mudança detectada nos arquivos gerados"
          else
            git commit -m "🔄 Atualizar mapeamento de permissões automaticamente
            
            Alterações: ${{ steps.changes.outputs.changes }}
            Total de ações: ${{ steps.stats.outputs.total_actions }}
            Arquivos analisados: ${{ steps.analyze.outputs.total_files }}
            
            Arquivos atualizados:
            - Mapeamento_Acoes_Funcionalidades_com_Constantes.xlsx
            - Mapeamento_Acoes_Funcionalidades_com_Constantes.csv
            - Mapeamento_Acoes_Funcionalidades_com_Constantes.html"
            
            git push
            echo "✅ Arquivos atualizados e commitados automaticamente"
          fi

      - name: 📋 Resumo da execução
        run: |
          echo "## 🎯 Resumo da Atualização de Permissões" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Alterações:** ${{ steps.changes.outputs.changes }}" >> $GITHUB_STEP_SUMMARY
          echo "**Total de arquivos analisados:** ${{ steps.analyze.outputs.total_files }}" >> $GITHUB_STEP_SUMMARY
          echo "**Total de ações mapeadas:** ${{ steps.stats.outputs.total_actions }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### ✅ Arquivos Atualizados:" >> $GITHUB_STEP_SUMMARY
          echo "- 📊 Mapeamento_Acoes_Funcionalidades_com_Constantes.xlsx" >> $GITHUB_STEP_SUMMARY
          echo "- 📄 Mapeamento_Acoes_Funcionalidades_com_Constantes.csv" >> $GITHUB_STEP_SUMMARY
          echo "- 🌐 Mapeamento_Acoes_Funcionalidades_com_Constantes.html" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🔧 Scripts Executados:" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Node.js: gerar_excel.cjs" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Python: gerar_planilha_acoes.py" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Node.js: gerar_csv_com_constantes.cjs" >> $GITHUB_STEP_SUMMARY

      - name: 📤 Upload dos arquivos gerados
        uses: actions/upload-artifact@v3
        with:
          name: mapeamento-permissoes-${{ github.run_number }}
          path: |
            src/ClientApp/*.xlsx
            src/ClientApp/*.csv
            src/ClientApp/*.html
          retention-days: 30
