const fs = require('fs');

// Mapeamento dos valores do enum para os tipos
const enumMapping = {
    0: 'SISTEMA_ADMIN',
    1: 'SISTEMA_FINANCEIRO',
    2: 'DESENVOLVEDOR',
    3: 'REVENDA_ADMIN',
    5: 'REVENDA_ASSISTENTE',
    6: 'SUPORTE_STI3',
    7: 'CANAIS_GERENTE',
    8: 'ANALISTA_CONTEUDO'
};

// Tipos de usuário na ordem das colunas
const tiposUsuario = [
    'SISTEMA_ADMIN',
    'SISTEMA_FINANCEIRO', 
    'DESENVOLVEDOR',
    'REVENDA_ADMIN',
    'RE<PERSON><PERSON><PERSON>_ASSISTENTE',
    'SUPORTE_STI3',
    'CANAIS_GERENTE',
    'ANALISTA_CONTEUDO'
];

// Dados das ações extraídos dos arquivos TypeScript
const acoesData = {
    // acesso_menu.ts - AcessoMenu
    'AcessoMenu.MENU_ASSINATURA': [0, 1, 7, 6, 3, 5, 2],
    'AcessoMenu.MENU_FATURAMENTO_EXIBICAO': [0, 1, 7, 6, 3, 2],
    'AcessoMenu.MENU_FATURAMENTO_CONFERENCIA': [0, 1, 7, 2],
    'AcessoMenu.MENU_REVENDA': [0, 7, 6, 2],
    'AcessoMenu.MENU_CADASTRO': [0, 7, 2],
    'AcessoMenu.MENU_CADASTRO_PRODUTO': [0, 2],
    'AcessoMenu.MENU_CADASTRO_SERVICO': [0, 2],
    'AcessoMenu.MENU_CADASTRO_GRADE_SERVICO': [0, 2, 7],
    'AcessoMenu.MENU_CADASTRO_USUARIO': [0, 7, 2],
    'AcessoMenu.MENU_ATUALIZACAO': [0, 2],
    'AcessoMenu.MENU_FISCAL': [0, 2],
    'AcessoMenu.MENU_FISCAL_REGRA_FISCAL': [0, 2],
    'AcessoMenu.MENU_FISCAL_NOTA_FISCAL_URL': [0, 2],
    'AcessoMenu.MENU_FISCAL_NFCE': [0, 2],
    'AcessoMenu.MENU_FISCAL_NF_REJEICAO': [0, 2],
    'AcessoMenu.MENU_FISCAL_NF_VALIDACAO': [0, 2],
    'AcessoMenu.MENU_IMPORTACAO': [0, 2],
    'AcessoMenu.MENU_LOG': [0, 2],
    'AcessoMenu.MENU_HANG_FIRE': [0, 2],
    'AcessoMenu.MENU_TREINAMENTOS': [8, 0, 2],
    'AcessoMenu.MENU_ARTIGOS_VIDEOS': [8, 0, 2],
    'AcessoMenu.MENU_TEMAS': [8, 0, 2],
    'AcessoMenu.MENU_CATEGORIAS_TREINAMENTO': [8, 0, 2],
    'AcessoMenu.MENU_BANNER': [8, 0, 2],
    
    // assinatura.ts - AssinaturaAcao
    'AssinaturaAcao.ASSINATURA_EXIBIR_BANCO_DADOS': [0, 2],
    'AssinaturaAcao.ENVIO_DE_LOGS': [0, 2],
    'AssinaturaAcao.CONTA_CLIENTE_ALTERAR': [0, 7, 2],
    'AssinaturaAcao.CONTA_CLIENTE_BLOQUEAR_DESBLOQUEAR': [0, 2],
    'AssinaturaAcao.CONTA_CLIENTE_VISUALIZAR_BANCO_DADOS': [0, 2],
    'AssinaturaAcao.CONTA_CLIENTE_CADASTRAR_LOJA': [0, 7, 2],
    'AssinaturaAcao.CONTA_CLIENTE_ALTERAR_REVENDA': [0, 7],
    'AssinaturaAcao.ASSINATURA_CADASTRAR': [0, 7, 2],
    'AssinaturaAcao.ASSINATURA_VALIDAR_BLOQUEAR': [0, 1, 7, 3, 2],
    'AssinaturaAcao.ASSINATURA_LIBERACAO_PROVISORIA': [0, 1, 7, 6, 3, 5, 2],
    'AssinaturaAcao.ASSINATURA_EXIBIR_FATURAMENTO': [0, 1, 7, 6, 3, 5, 2],
    'AssinaturaAcao.ASSINATURA_GERAR_FATURAMENTO': [0, 2],
    'AssinaturaAcao.ASSINATURA_ALTERAR': [0, 7, 2],
    'AssinaturaAcao.ASSINATURA_DIA_VENCIMENTO': [0, 1, 2],
    'AssinaturaAcao.ASSINATURA_ALTERAR_SERVICO': [0, 7, 2],
    'AssinaturaAcao.ASSINATURA_EXCLUIR_SERVICO': [0, 7, 2],
    'AssinaturaAcao.ASSINATURA_CANCELAR_REATIVAR': [0, 7, 2],
    
    // atualizacao.ts - AtualizacaoAcao
    'AtualizacaoAcao.CADASTRAR_ATUALIZACOES': [0, 2],
    'AtualizacaoAcao.ALTERAR_ATUALIZACOES': [0, 2],
    'AtualizacaoAcao.EXCLUIR_ATUALIZACOES': [0, 2],
    'AtualizacaoAcao.VISUALIZAR_GERENCIAR_ATUALIZACAO': [0, 2],
    'AtualizacaoAcao.ATUALIZAR_GERENCIAR_ATUALIZACAO': [0, 2],
    'AtualizacaoAcao.ATUALIZAR_TODOS_GERENCIAR_ATUALIZACAO': [0, 2],
    
    // cadastro_gradeservico.ts - CadastroGradeServicoAcao
    'CadastroGradeServicoAcao.VISUALIZAR_GRADE_SERVICOS': [0, 2],
    'CadastroGradeServicoAcao.CADASTRAR_GRADE_SERVICOS': [0, 2],
    'CadastroGradeServicoAcao.ALTERAR_GRADE_SERVICOS': [0, 2],
    'CadastroGradeServicoAcao.EXCLUIR_GRADE_SERVICOS': [0, 2],
    
    // cadastro_produto.ts - CadastroProdutoAcao
    'CadastroProdutoAcao.VISUALIZAR_PRODUTOS': [0, 2],
    'CadastroProdutoAcao.CADASTRAR_PRODUTOS': [0, 2],
    'CadastroProdutoAcao.ALTERAR_PRODUTOS': [0, 2],
    'CadastroProdutoAcao.EXCLUIR_PRODUTOS': [0, 2],
    
    // cadastro_revenda.ts - CadastroRevendaAcao
    'CadastroRevendaAcao.CADASTRAR_REVENDAS': [0, 7, 2],
    'CadastroRevendaAcao.LOGAR_REVENDAS': [0, 7, 2, 6],
    'CadastroRevendaAcao.ALTERAR_REVENDAS': [0, 7, 3, 2],
    'CadastroRevendaAcao.EXCLUIR_REVENDAS': [0, 7, 2],
    'CadastroRevendaAcao.PARAMETROS_STI3_REVENDAS': [0, 2],
    
    // cadastro_servico.ts - CadastroServicoAcao
    'CadastroServicoAcao.VISUALIZAR_SERVICOS': [0, 2],
    'CadastroServicoAcao.CADASTRAR_SERVICOS': [0, 2],
    'CadastroServicoAcao.ALTERAR_SERVICOS': [0, 2],
    'CadastroServicoAcao.EXCLUIR_SERVICOS': [0, 2],
    
    // cadastro_tabelapreco.ts - CadastroTabelaPrecoAcao
    'CadastroTabelaPrecoAcao.VISUALIZAR_TABELA_PRECOS': [0, 2],
    'CadastroTabelaPrecoAcao.ALTERAR_TABELA_PRECOS': [0, 2],
    'CadastroTabelaPrecoAcao.CADASTRAR_TABELA_PRECOS': [0, 2],
    'CadastroTabelaPrecoAcao.EXCLUIR_TABELA_PRECOS': [0, 2],
    'CadastroTabelaPrecoAcao.ALTERAR_INFO_TABELA_PRECOS': [0, 2],
    
    // cadastro_usuario.ts - CadastroUsuarioAcao
    'CadastroUsuarioAcao.USUARIO_LISTAGEM': [0, 2, 7, 6],
    'CadastroUsuarioAcao.USUARIO_CADASTRAR': [0, 7, 2],
    'CadastroUsuarioAcao.USUARIO_ALTERAR': [0, 2, 7, 3, 6],
    'CadastroUsuarioAcao.USUARIO_EXCLUIR': [0, 7, 2],
    
    // faturamento.ts - FaturamentoAcao
    'FaturamentoAcao.FATURAMENTO_VER_DETALHES': [0, 1, 7, 6, 3, 5, 2],
    'FaturamentoAcao.FATURAMENTO_EXCLUIR': [0, 2],
    'FaturamentoAcao.FATURAMENTO_EXIBIR': [0, 1, 7, 6, 3, 5, 2],
    'FaturamentoAcao.FATURAMENTO_LANCAR_VALOR': [0, 1, 2],
    'FaturamentoAcao.FATURAMENTO_CONFERENCIA': [0, 1, 7, 2],
    
    // fiscal_nfce.ts - FicalNFCeAcao
    'FicalNFCeAcao.VISUALIZAR_NFCE': [0, 2],
    'FicalNFCeAcao.CADASTRAR_NFCE': [0, 2],
    'FicalNFCeAcao.ALTERAR_NFCE': [0, 2],
    'FicalNFCeAcao.EXCLUIR_NFCE': [0, 2],
    
    // fiscal_notafiscalrejeicao.ts - NotaFiscalRejeicaoAcao
    'NotaFiscalRejeicaoAcao.VISUALIZAR_NF_REJEICAO': [0, 2],
    'NotaFiscalRejeicaoAcao.CADASTRAR_NF_REJEICAO': [0, 2],
    'NotaFiscalRejeicaoAcao.ALTERAR_NF_REJEICAO': [0, 2],
    'NotaFiscalRejeicaoAcao.EXCLUIR_NF_REJEICAO': [0, 2],
    
    // fiscal_notafiscalurl.ts - NotaFiscalUrlAcao
    'NotaFiscalUrlAcao.VISUALIZAR_NF_SERVICO': [0, 2],
    'NotaFiscalUrlAcao.CADASTRAR_NF_SERVICO': [0, 2],
    'NotaFiscalUrlAcao.ALTERAR_NF_SERVICO': [0, 2],
    'NotaFiscalUrlAcao.EXCLUIR_NF_SERVICO': [0, 2],
    
    // fiscal_notafiscalvalidacao.ts - NotaFicalValidacaoAcao
    'NotaFicalValidacaoAcao.VISUALIZAR_NF_VALIDACOES': [0, 2],
    'NotaFicalValidacaoAcao.CADASTRAR_NF_VALIDACOES': [0, 2],
    'NotaFicalValidacaoAcao.ALTERAR_NF_VALIDACOES': [0, 2],
    'NotaFicalValidacaoAcao.EXCLUIR_NF_VALIDACOES': [0, 2],
    
    // fiscal_regrafiscalurl.ts - NotaFiscalRegraUrlAcao
    'NotaFiscalRegraUrlAcao.VISUALIZAR_REGRAS_FISCAIS': [0, 2],
    'NotaFiscalRegraUrlAcao.CADASTRAR_REGRAS_FISCAIS': [0, 2],
    'NotaFiscalRegraUrlAcao.ALTERAR_REGRAS_FISCAIS': [0, 2],
    'NotaFiscalRegraUrlAcao.EXCLUIR_REGRAS_FISCAIS': [0, 2],
    
    // importacao_ncm.ts - ImportacaoNcmAcao
    'ImportacaoNcmAcao.VISUALIZAR_IMPORTACAO_NCM': [0, 2],
    'ImportacaoNcmAcao.CADASTRAR_IMPORTACAO_NCM': [0, 2],
    
    // log_logerros.ts - LogErroAcao
    'LogErroAcao.EXCLUIR_LOG_ERROS': [0, 2],
    'LogErroAcao.EXCLUIR_TODOS_LOG_ERROS': [0, 2],
    
    // zenflix.ts - Zenflix
    'Zenflix.VISUALIZAR_VIDEOS': [8, 2, 0],
    'Zenflix.CADASTRAR_VIDEOS': [8, 2, 0],
    'Zenflix.ALTERAR_VIDEOS': [8, 2, 0],
    'Zenflix.EXCLUIR_VIDEOS': [8, 2, 0],
    'Zenflix.VISUALIZAR_TEMAS': [8, 2, 0],
    'Zenflix.CADASTRAR_TEMAS': [8, 2, 0],
    'Zenflix.ALTERAR_TEMAS': [8, 2, 0],
    'Zenflix.EXCLUIR_TEMAS': [8, 2, 0],
    'Zenflix.VISUALIZAR_BANNERS': [8, 2, 0],
    'Zenflix.CADASTRAR_BANNERS': [8, 2, 0],
    'Zenflix.ALTERAR_BANNERS': [8, 2, 0],
    'Zenflix.EXCLUIR_BANNERS': [8, 2, 0],
    'Zenflix.VISUALIZAR_CATEGORIAS': [8, 2, 0],
    'Zenflix.CADASTRAR_CATEGORIAS': [8, 2, 0],
    'Zenflix.ALTERAR_CATEGORIAS': [8, 2, 0],
    'Zenflix.EXCLUIR_CATEGORIAS': [8, 2, 0],
    'Zenflix.VISUALIZAR_ARTIGOS': [8, 2, 0],
    'Zenflix.CADASTRAR_ARTIGOS': [8, 2, 0],
    'Zenflix.ALTERAR_ARTIGOS': [8, 2, 0],
    'Zenflix.EXCLUIR_ARTIGOS': [8, 2, 0],
};

function criarCSV() {
    console.log('Criando arquivo CSV com nomes das constantes...');
    
    // Cabeçalho
    let csvContent = ['Ação', ...tiposUsuario].join(',') + '\n';
    
    // Ordenar ações alfabeticamente
    const acoesOrdenadas = Object.keys(acoesData).sort();
    
    // Processar cada ação
    acoesOrdenadas.forEach(acao => {
        const usuariosEnum = acoesData[acao];
        const linha = [acao];
        
        // Para cada tipo de usuário, verificar se tem permissão
        tiposUsuario.forEach(tipo => {
            // Encontrar o valor enum correspondente ao tipo
            const enumValue = Object.keys(enumMapping).find(key => enumMapping[key] === tipo);
            const temPermissao = usuariosEnum.includes(parseInt(enumValue));
            linha.push(temPermissao ? 'SIM' : 'NÃO');
        });
        
        csvContent += linha.join(',') + '\n';
    });
    
    // Salvar arquivo
    const nomeArquivo = 'Mapeamento_Acoes_Funcionalidades_com_Constantes.csv';
    fs.writeFileSync(nomeArquivo, csvContent, 'utf8');
    
    console.log(`✅ Arquivo CSV criado com sucesso: ${nomeArquivo}`);
    console.log(`📊 Total de ações mapeadas: ${acoesOrdenadas.length}`);
    
    return nomeArquivo;
}

// Executar a função
try {
    criarCSV();
} catch (error) {
    console.error('❌ Erro ao criar CSV:', error);
}
